import requests
from parsel import Selector
import csv
import time
import re

INPUT_FILE = "extrait_10001_20001_part_1.csv"
OUTPUT_FILE = "part-1-data.csv"

RAW_NUMBER = 0
SKIP_RAW = 0 # Start from beginning

# Read the CSV file
data_rows = []

with open(INPUT_FILE, 'r', encoding='utf-8') as file:
    csv_reader = csv.DictReader(file)
    headers = csv_reader.fieldnames
    
    for row in csv_reader:
        data_rows.append(row)


headers = {
    "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
    "accept-encoding": "gzip, deflate, br, zstd",
    "accept-language": "en-US,en;q=0.9,sv;q=0.8,bn;q=0.7",
    "dnt": "1",
    "priority": "u=0, i",
    "referer": "https://infonet.fr/",
    "sec-ch-ua": '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": '"Linux"',
    "sec-fetch-dest": "document",
    "sec-fetch-mode": "navigate",
    "sec-fetch-site": "same-origin",
    "sec-fetch-user": "?1",
    "upgrade-insecure-requests": "1",
    "user-agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
}

cookies = {
    "infonet": "qqvb8m5ns14ouat1iqqojathvi",
    "trustedsite_visit": "1",
    "_ga": "GA1.1.558240977.1753774017",
    "axeptio_authorized_vendors": "%2CBing%2Cgoogle_analytics%2Cgoogle_ads%2Cbing%2CGoogle_Ads%2C",
    "axeptio_all_vendors": "%2CBing%2Cgoogle_analytics%2Cgoogle_ads%2Cbing%2CGoogle_Ads%2C",
    "axeptio_cookies": '{"$$token":"nrqh5thek3ottxm94yg0zk","$$date":"2025-07-29T07:27:17.991Z","$$cookiesVersion":{"name":"infonet website-fr","identifier":"6285f2013d207f8f42a5bad9"},"Bing":true,"google_analytics":true,"google_ads":true,"$$googleConsentMode":{"version":2,"analytics_storage":"granted","ad_storage":"granted","ad_user_data":"granted","ad_personalization":"granted"},"bing":true,"Google_Ads":true,"$$completed":true}',
    "aws-waf-token": "4c0c8d68-dde3-4484-ac2c-705f0f5e0b98:CgoAk+86ZA48AAAA:3PkjrHaYOl3gKph/uW3i6yUMA3yQWau4G/l4sIjnlG9X/fLHECPo6flMbOQACrZhTZ8am789abwHpPQJQJTKVnEw9cyb5CBYme9HbvVAAtNFqGVEs3x18WuGP61DWaIOzYhk44a3X3MWg8r1zBRexqVQz1tzrRxqs1Osr9vDse5VzKy9Q42rD2TCT09ZzlNJdWg=",
    "_gcl_au": "1.1.1240712368.1753774038.1952344250.1753777464.1753777656",
    "_uetsid": "750393806c4d11f0933d716a6db32fef|1mpn827|2|fy0|0|2036",
    "_ga_N4YEQJPVC9": "GS2.1.s1753808421$o6$g1$t1753808421$j60$l0$h878060388",
    "_uetvid": "750391306c4d11f0bbda75f5a60d5405|p2xfku|1753808422412|1|1|bat.bing.com/p/insights/c/j"
}



# Prepare CSV file with headers (create/overwrite the file)
original_fieldnames = list(data_rows[0].keys()) if data_rows else []
new_fieldnames = ['website', 'phone', "email"]
all_fieldnames = original_fieldnames + new_fieldnames

# Create CSV file and write header
with open(OUTPUT_FILE, 'w', newline='', encoding='utf-8') as csvfile:
    writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
    writer.writeheader()


try:
    from unidecode import unidecode       # pip install unidecode
except ImportError:
    def unidecode(txt):                   # graceful fallback
        return txt.encode("ascii", "ignore").decode()
    

def build_infonet_url(siret: str, denomination: str) -> str:
    """
    Return the canonical Infonet URL for *siret* + *denominationUniteLegale*.
    
    1. Transliterate any accents to ASCII and lowercase the text.
    2. Replace every run of non‑alphanumeric characters with a single dash.
    3. Trim leading/trailing dashes.
    """
    slug = re.sub(r"[^a-z0-9]+", "-", unidecode(denomination).lower()).strip("-")
    return f"https://infonet.fr/entreprises/{siret}-{slug}/"

# Process each row and save immediately
processed_count = 0
for index, row in enumerate(data_rows):
    if index < SKIP_RAW:
        continue

    siret = row['siret']
    denominationUniteLegale = row['denominationUniteLegale']
    verif_url = build_infonet_url(siret, denominationUniteLegale)
    
    
    current_row = index + 1
    print(f"Processing Raw Number: {RAW_NUMBER} ...")
    RAW_NUMBER += 1
    

    # Initialize default values
    website = None
    phone = None
    email = None


    response = requests.get(verif_url, headers=headers, cookies=cookies)
    time.sleep(1)

    if response.status_code == 202:
        # Try to get status URL from Location header first
        status_url = response.headers.get('Location')
        
        # If no Location header, try to parse JSON for StatusUrl
        if not status_url:
            try:
                json_data = response.json()
                status_url = json_data.get('StatusUrl')
            except (ValueError, requests.exceptions.JSONDecodeError):
                # If JSON parsing fails, retry the original URL with longer waits
                print(f"Warning: 202 response but no Location header and invalid JSON for {verif_url}")
                print(f"Retrying original URL with longer waits...")
                status_url = None
        
        # If we have a status URL, poll it
        if status_url:
            max_retries = 30  # Maximum number of retries
            retry_count = 0
            while retry_count < max_retries:
                time.sleep(2)  # Wait 2 seconds between retries
                status_resp = requests.get(status_url, headers=headers, cookies=cookies)
                if status_resp.status_code == 200:
                    html = status_resp.text
                    break
                elif status_resp.status_code == 202:
                    retry_count += 1
                    print(f"Still processing... retry {retry_count}/{max_retries}")
                else:
                    print(f"Unexpected status code {status_resp.status_code} from status URL")
                    break
            else:
                print(f"Max retries reached for status URL, using original response")
                html = response.text
        else:
            # No status URL available, retry the original URL with longer waits
            max_retries = 10
            retry_count = 0
            while retry_count < max_retries:
                time.sleep(3)  # Wait 3 seconds between retries
                retry_resp = requests.get(verif_url, headers=headers, cookies=cookies)
                if retry_resp.status_code == 200:
                    html = retry_resp.text
                    response = retry_resp  # Update response for status_code logging
                    break
                elif retry_resp.status_code == 202:
                    retry_count += 1
                    print(f"Still 202... retry {retry_count}/{max_retries}")
                else:
                    print(f"Got status code {retry_resp.status_code}, stopping retries")
                    html = retry_resp.text
                    response = retry_resp
                    break
            else:
                print(f"Max retries reached, still getting 202")
                html = response.text
    else:
        # For non-202 responses, use the original response
        html = response.text
    selector = Selector(html)
    phone = selector.xpath("//span[@id='header-company-phone']//a[starts-with(@href, 'tel:')]/text()").get()
    email = selector.xpath("//span[@id='header-company-email']//a[starts-with(@href, 'mailto:')]/text()").get()
    website = selector.xpath("//span[@id='header-company-website']//a/@href").get()
    status_code = response.status_code


    print(phone)
    print(email)
    print(website)
    print(status_code)
    print(verif_url)

    # print(f"{phone}, {email}, {website}, {status_code}, {verif_url}, == {RAW_NUMBER} ==", end="")


    # Prepare the result row (combining original row data with new extracted data)
    result_row = row.copy()
    result_row.update({
        'website': website,
        'phone': phone,
        "email": email
    })
    
    
    # Save immediately to CSV file (append mode)
    try:
        with open(OUTPUT_FILE, 'a', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=all_fieldnames)
            writer.writerow(result_row)
        processed_count += 1
    except Exception as e:
        print(f"  ✗ Error saving to CSV: {str(e)}")

print(f"\nProcessing complete! Processed and saved {processed_count} records to {OUTPUT_FILE}.")





import requests

SIRET = "53886522100039"
url   = f"https://entreprise.data.gouv.fr/api/sirene/v3/etablissements/{SIRET}"
data  = requests.get(url, timeout=20).json()
print(data["etablissement"]["unite_legale"]["denomination"])



















# url = "https://infonet.fr/entreprises/54205118000066-totalenergies-se/"
# response = requests.get(url, headers=headers, cookies=cookies)


# html = response.text

# selector = Selector(html)

# phone = selector.xpath("//span[@id='header-company-phone']//a[starts-with(@href, 'tel:')]/text()").get()
# email = selector.xpath("//span[@id='header-company-email']//a[starts-with(@href, 'mailto:')]/text()").get()
# website = selector.xpath("//span[@id='header-company-website']//a/@href").get()
# status_code = response.status_code


# print(phone)
# print(email)
# print(website)
print(status_code)
















