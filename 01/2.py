#!/usr/bin/env python3
import base64, requests, csv, time

# 1) ----- credentials -----
CLIENT_ID     = "7cd3ee68-09d0-41c8-896a-35b191e933cd"
CLIENT_SECRET = "v2fRLN2H7aVamXmMpqeaUeEDA6gbvYvQ"
TOKEN_URL     = "https://api.insee.fr/token"

auth_header = base64.b64encode(f"{CLIENT_ID}:{CLIENT_SECRET}".encode()).decode()
token_resp  = requests.post(
    "https://api.insee.fr/token",
    headers={"Authorization": f"Basic {auth_header}"},
    data={"grant_type": "client_credentials"},
    timeout=10
)
token_resp.raise_for_status()
TOKEN = token_resp.json()["access_token"]



# 2) ----- query the /siret/ endpoint -----
SIRETS = [
    "53886522100039",  # LA PROMO DU JOUR ‑ DIMONA MARKETING
    "53886579100015",  # MAILLET DISTRIBUTION
    # "53886752400018",  # P&C ASSOCIATES
    # "53886822500029",  # EURL JACQ LORENZON
]

API_BASE = "https://api.insee.fr/entreprises/sirene/V3/siret/"
headers  = {"Authorization": f"Bearer {TOKEN}"}

with open("sirene_output.csv", "w", newline="", encoding="utf‑8") as f:
    writer = csv.DictWriter(
        f,
        fieldnames=["siret", "denomination", "naf_code", "creation_date", "address"]
    )
    writer.writeheader()

    for siret in SIRETS:
        r = requests.get(API_BASE + siret, headers=headers, timeout=10)
        if r.status_code == 429:                     # too many calls‑per‑minute
            time.sleep(int(r.headers.get("Retry‑After", 60)))
            r = requests.get(API_BASE + siret, headers=headers, timeout=10)
        r.raise_for_status()

        e = r.json()["etablissement"]
        writer.writerow({
            "siret":          siret,
            "denomination":   e["uniteLegale"]["denominationUniteLegale"],
            "naf_code":       e["activitePrincipaleEtablissement"],
            "creation_date":  e["dateCreationEtablissement"],
            "address":        e["adresseEtablissement"]["libelleAdresseEtablissement"]
        })

print("✓ CSV saved as sirene_output.csv")
